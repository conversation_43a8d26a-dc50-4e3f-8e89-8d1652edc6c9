---
type: "always_apply"
---

# Agent 通用开发指南 (global_guidelines.md)

本文档为在 IDE 中使用的 Augment AI Agent 提供一套通用的规则和指南。请严格遵守这些指南以确保项目代码的统一、规范和高质量。

---

## Agent 交互准则

**【禁止废话】**: 你的回答**只应包含代码**。除非我特别要求，否则不要添加任何解释、注释或总结。

---

## 核心规则 (Always - 始终遵循)

这些是最高优先级的规则，你在每一次交互中都必须严格遵守。

1.  **【遵循约定】**: 你的首要任务是理解并严格遵守项目中已有的结构、命名约定和编码风格。绝不引入任何破坏现有约定的变更。
2.  **【严禁备用方案】**: 对于任何功能，只提供一种最直接、最简单的实现路径。**绝对禁止**提供备用方案、多种尝试方法、fallback 逻辑或"如果A失败则尝试B"的代码结构。
3.  **【最少代码】**: 只编写为实现我要求所必需的最少代码。当用户要求"添加功能"时，只实现一个最基本的测试用例，除非用户明确要求更多。
4.  **【立即失败原则】**: 当检测到错误或异常情况时，立即停止执行并明确报告问题。绝不隐藏错误或尝试绕过问题。
5.  **【严禁容错逻辑】**: 绝不使用复杂的异常处理、条件分支或容错机制来掩盖问题。代码必须在第一次尝试失败时立即停止，不得尝试其他方法。
6.  **【最小可行实现】**: 当用户要求添加功能时，只实现一个最基本的测试用例。绝不主动添加多个测试场景、边界情况或错误处理测试，除非用户明确要求。
7.  **【禁止多路径代码】**: 绝不编写包含多个执行路径的代码。禁止使用"备用方案1"、"备用方案2"、"如果失败则尝试"等任何形式的多重尝试逻辑。
8.  **【先分析，后行动】**: 在创建或修改任何文件之前，你必须先读取相关的现有文件来理解上下文。当用户要求"先探索，再写代码"时，必须严格按此顺序执行。

---

## 通用异常处理原则

**唯一正确的方式**:
- 使用一种最直接的方法尝试操作
- 检查操作是否成功，失败时立即停止
- 使用明确的错误描述说明问题所在
- 绝不尝试第二种方法

**严格禁止的方式**:
- 编写"备用方案1"、"备用方案2"等多重尝试逻辑
- 使用"如果A失败则尝试B"的代码结构
- 编写容错机制或恢复策略
- 使用循环或条件分支来尝试多种方法
- 隐藏错误信息或继续执行有问题的流程