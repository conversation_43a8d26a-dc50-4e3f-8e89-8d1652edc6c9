import pytest

from allm_helper.chat_helper import <PERSON><PERSON><PERSON><PERSON><PERSON>
from allm_helper.platforms.aistudio_driver import Aist<PERSON>o<PERSON>river
from allm_helper.platforms.cozecn_driver import CozeCNDriver
from allm_helper.platforms.doubao_driver import DoubaoDriver
from allm_helper.platforms.gemini_driver import Gemini<PERSON><PERSON>
from allm_helper.platforms.monica_driver import Monica<PERSON><PERSON>
from allm_helper.platforms.notebooklm_driver import NotebookLMDriver
from allm_helper.platforms.yuanbao_driver import YuanbaoDriver


@pytest.fixture(scope="session")
def chat_helper_factory(get_user_page):
    """
    一个创建 ChatHelper 实例的工厂 fixture.
    这样使得测试用例可以灵活地为不同平台和用户创建 agents.
    """
    created_helpers = []

    def _create_chat_helper(platform_name: str, user_email: str, conversation_params: dict = None):
        """
        创建并初始化一个 ChatHelper.

        :param platform_name: 平台名称, e.g., "monica", "aistudio"
        :param user_email: 用户邮箱
        :param conversation_params: 创建新对话时使用的参数
        :return: Chat<PERSON>elper 实例
        """
        page = get_user_page(platform_name, user_email)

        driver_map = {
            "monica": MonicaDriver,
            "aistudio": AistudioDriver,
            "cozecn": CozeCNDriver,
            "doubao": DoubaoDriver,
            "gemini": GeminiDriver,
            "notebooklm": NotebookLMDriver,
            "yuanbao": YuanbaoDriver,
            # 未来可以在这里添加更多的 driver
        }

        driver_class = driver_map.get(platform_name.lower())
        if not driver_class:
            raise ValueError(f"不支持的平台: {platform_name}")

        driver = driver_class(page)
        driver.new_conversation(conversation_params or {})

        helper = ChatHelper(driver)
        created_helpers.append(helper)
        return helper

    yield _create_chat_helper
