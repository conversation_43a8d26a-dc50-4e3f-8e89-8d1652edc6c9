import re
from playwright.sync_api import Page
from typing import Optional, List

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor


class DoubaoDriver(BasePlatformDriver):
    """
    豆包平台的具体驱动实现。
    """
    CHAT_URL = "https://www.doubao.com/chat/"

    def __init__(self, page: Page):
        """
        初始化豆包驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)

        # 默认配置值
        self.current_model_config = {
            'enable_deep_thinking': False,
        }

        # 文件上传相关设置
        self.upload_timeout = 10000  # 文件上传超时时间（毫秒）

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - enable_deep_thinking: 是否启用深度思考
        """
        if model_config:
            self.current_model_config.update(model_config)

        # 等待页面完全加载
        self.page.wait_for_timeout(2000)

        # 点击新对话按钮
        new_chat_button = self.page.locator('button').filter(has_text="新对话").first
        if new_chat_button.is_visible():
            new_chat_button.click()
            self.page.wait_for_timeout(1000)

        # 应用模型配置
        self._apply_model_config()
        return self

    def use_existing_conversation(self, conversation_title: str):
        """使用已有会话"""
        # 在侧边栏查找指定标题的会话
        conversation_link = self.page.locator(f'text="{conversation_title}"').first
        assert conversation_link.is_visible(), f"未找到标题为 '{conversation_title}' 的会话"

        conversation_link.click()
        self.page.wait_for_timeout(2000)



    def _apply_model_config(self):
        """应用模型配置"""
        # 设置深度思考
        if 'enable_deep_thinking' in self.current_model_config:
            enable_thinking = self.current_model_config['enable_deep_thinking']
            self._toggle_deep_thinking(enable_thinking)

    def _toggle_deep_thinking(self, enable: bool):
        """切换深度思考功能"""
        # 查找深度思考按钮
        deep_thinking_button = self.page.locator('button').filter(has_text=re.compile(r'深度思考')).first
        if not deep_thinking_button.is_visible():
            return  # 如果找不到深度思考按钮，直接返回

        # 获取当前状态
        button_text = deep_thinking_button.text_content()
        current_enabled = "开启" in button_text or "自动" in button_text

        # 如果当前状态与期望状态不同，则点击切换
        if current_enabled != enable:
            deep_thinking_button.click()
            self.page.wait_for_timeout(1000)

    def _upload_files(self, files: List[str]):
        """
        上传文件到豆包

        Args:
            files: 要上传的文件路径列表
        """
        # 查找文件输入元素
        file_input = self.page.locator('input[type="file"]').first
        assert file_input.count() > 0, "找不到文件上传输入框"

        file_input.set_input_files(files)
        self.page.wait_for_timeout(2000)

    def _wait_for_response_complete(self):
        """等待响应完成"""
        # 先等待3秒让消息发送
        self.page.wait_for_timeout(3000)

        # 等待AI回复出现（通过检测回复操作按钮的出现）
        copy_button_selector = '[data-testid="message_action_copy"]'

        # 尝试等待复制按钮出现，如果超时则继续
        if self.page.locator(copy_button_selector).count() > 0:
            # 如果已经有复制按钮，等待新的回复完成
            self.page.wait_for_timeout(2000)
        else:
            # 等待复制按钮出现，表示AI已经回复
            self.page.wait_for_selector(copy_button_selector, timeout=60000)

    def _get_reply_by_copy(self):
        """通过复制功能获取回复"""
        # 使用豆包特定的复制按钮
        copy_button = self.page.locator('[data-testid="message_action_copy"]').last
        assert copy_button.is_visible(), "找不到复制按钮"

        with UniversalClipboardInterceptor(self.page) as interceptor:
            copy_button.click()
            interceptor.wait_for_capture(timeout=5.0)
            assert interceptor.text, "复制操作失败，未获取到内容"
            return interceptor.text

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text"。
        :return: AI的回复文本。
        """
        # 首先切换到当前驱动的页面
        self.switch_to_page()

        # 1. 上传附件 (如果需要)
        if attachments:
            self._upload_files(attachments)

        # 2. 输入提示
        # 使用豆包特定的输入框选择器
        input_element = self.page.locator('[data-testid="chat_input_input"]').first
        assert input_element.is_visible(), "找不到豆包输入框"

        # 清空输入框并输入新文本
        input_element.click()
        input_element.fill("")  # 先清空
        input_element.fill(prompt)

        # 等待一下让发送按钮变为可用状态
        self.page.wait_for_timeout(500)

        # 3. 发送消息 - 使用回车键发送
        input_element.press("Enter")

        # 4. 等待回复完成并获取回复
        self._wait_for_response_complete()
        return self._get_reply_by_copy()

    def is_ready(self) -> bool:
        """检查平台是否准备好接收新消息"""
        # 检查用户头像按钮是否存在（表示已登录）
        avatar_button = self.page.locator('[data-testid="chat_header_avatar_button"]').first
        if not avatar_button.is_visible():
            return False

        # 检查输入框是否可用
        input_element = self.page.locator('[data-testid="chat_input_input"]').first
        return input_element.is_visible()

    def save_chat(self, chat_name: str):
        """保存当前会话并命名"""
        # 豆包会自动保存会话，这里可以实现重命名功能
        pass

    def del_chat(self, chat_name: str):
        """删除会话"""
        # 在侧边栏找到指定会话并删除
        conversation_item = self.page.locator(f'text="{chat_name}"').first
        if not conversation_item.is_visible():
            return  # 如果找不到会话，直接返回

        # 右键点击或查找删除按钮
        conversation_item.click(button="right")
        self.page.wait_for_timeout(500)
        delete_button = self.page.locator('button').filter(has_text=re.compile(r'删除|delete')).first
        if delete_button.is_visible():
            delete_button.click()
            self.page.wait_for_timeout(1000)
