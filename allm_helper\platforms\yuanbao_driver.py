import re
from playwright.sync_api import Page
from typing import Optional, List

from allm_helper.platforms.base_platform import BasePlatformDriver
from autils.pw_clipboard_helper import UniversalClipboardInterceptor


class YuanbaoDriver(BasePlatformDriver):
    """
    腾讯元宝平台的具体驱动实现。
    """
    CHAT_URL = "https://yuanbao.tencent.com/chat"

    def __init__(self, page: Page):
        """
        初始化腾讯元宝驱动，只接受 page 参数。
        模型配置将在 new_conversation 时传入。
        """
        super().__init__(page)
        self.page.goto(self.CHAT_URL)

        # 默认配置值
        self.current_model_config = {
            'model_name': "混元大模型",
        }

        # 文件上传相关设置
        self.upload_timeout = 10000  # 文件上传超时时间（毫秒）

    def new_conversation(self, model_config: Optional[dict] = None):
        """
        创建新会话，并应用模型配置。

        Args:
            model_config: 模型配置字典，包含：
                - model_name: 模型名称
                - use_temp_session: 是否使用临时会话
                - enable_deep_thinking: 是否启用深度思考
                - enable_web_search: 是否启用联网搜索
        """
        if model_config:
            self.current_model_config.update(model_config)

        # 总是应用模型配置（即使是默认配置）
        self._apply_model_config()
        return self

    def use_existing_conversation(self, conversation_title: str):
        """使用已有会话"""
        pass

    def _apply_model_config(self):
        """应用模型配置"""
        # 等待页面完全加载
        self.page.wait_for_timeout(2000)

        # 1. 处理临时会话
        if self.current_model_config.get('use_temp_session', False):
            self._enable_temp_session()

        # 2. 选择模型
        model_name = self.current_model_config.get('model_name')
        if model_name:
            self._select_model(model_name)

        # 3. 设置深度思考
        if 'enable_deep_thinking' in self.current_model_config:
            enable_thinking = self.current_model_config['enable_deep_thinking']
            self._toggle_deep_thinking(enable_thinking)

        # 4. 设置联网搜索
        if 'enable_web_search' in self.current_model_config:
            enable_search = self.current_model_config['enable_web_search']
            self._toggle_web_search(enable_search)

    def _enable_temp_session(self):
        """启用临时会话"""
        # 尝试多种方式查找临时会话按钮
        self.page.evaluate("""() => {
            // 方法1: 通过类名查找
            let tempButton = document.querySelector('.icon-yb-ic_temporary_20');
            if (tempButton) {
                tempButton.click();
                return;
            }

            // 方法2: 通过包含临时的文本查找
            const allElements = document.querySelectorAll('*');
            for (let element of allElements) {
                if (element.title && element.title.includes('临时')) {
                    element.click();
                    return;
                }
            }

            // 方法3: 查找可能的临时会话图标
            const icons = document.querySelectorAll('i, span, div');
            for (let icon of icons) {
                if (icon.className && icon.className.includes('temporary')) {
                    icon.click();
                    return;
                }
            }
        }""")

        self.page.wait_for_timeout(3000)  # 等待页面变化

    def _select_model(self, model_name: str):
        """选择模型"""
        # 检查当前模型是否已经是目标模型
        if self._verify_model_selection(model_name):
            return

        # 点击模型选择按钮
        model_button = self.page.locator('button').filter(has_text=model_name).first
        if not model_button.is_visible():
            # 如果当前模型不是目标模型，点击模型选择区域
            current_model_button = self.page.locator('button[class*="model"], button').filter(has_text=re.compile(r'(Hunyuan|DeepSeek)')).first
            if current_model_button.is_visible():
                current_model_button.click()
                self.page.wait_for_timeout(500)

                # 在下拉菜单中选择目标模型
                target_option = self.page.locator('listitem').filter(has_text=model_name).first
                if target_option.is_visible():
                    target_option.click()
                    self.page.wait_for_timeout(1000)

                    # 验证模型是否切换成功
                    if not self._verify_model_selection(model_name):
                        raise Exception(f"模型切换失败，期望: {model_name}")

    def _toggle_deep_thinking(self, enable: bool):
        """切换深度思考功能"""
        current_state = self._verify_deep_thinking()
        if current_state != enable:
            deep_thinking_button = self.page.locator('button').filter(has_text="深度思考").first
            if deep_thinking_button.is_visible():
                deep_thinking_button.click()
                self.page.wait_for_timeout(1000)

                # 验证状态是否正确切换
                if self._verify_deep_thinking() != enable:
                    print(f"深度思考功能切换可能失败，期望: {enable}, 实际: {self._verify_deep_thinking()}")

    def _toggle_web_search(self, enable: bool):
        """切换联网搜索功能"""
        if enable:  # 只在启用时尝试点击
            # 尝试多种方式查找联网搜索按钮
            self.page.evaluate("""() => {
                // 方法1: 通过文本查找联网搜索
                const allElements = document.querySelectorAll('*');
                for (let element of allElements) {
                    if (element.textContent && element.textContent.trim() === '联网搜索') {
                        // 查找可点击的父级元素
                        let clickable = element;
                        while (clickable && clickable.tagName !== 'BUTTON' && !clickable.onclick) {
                            clickable = clickable.parentElement;
                        }
                        if (clickable) {
                            clickable.click();
                            return;
                        }
                    }
                }

                // 方法2: 查找包含网络相关的按钮
                const buttons = document.querySelectorAll('button, div[role="button"]');
                for (let button of buttons) {
                    if (button.textContent && (button.textContent.includes('联网') || button.textContent.includes('搜索'))) {
                        button.click();
                        return;
                    }
                }
            }""")

            self.page.wait_for_timeout(2000)  # 等待状态变化

    def _verify_temp_session(self) -> bool:
        """验证临时会话是否启用"""
        current_url = self.page.url
        page_title = self.page.title()

        # 检查URL参数
        url_check = 'chatMode=temp' in current_url

        # 检查页面标题
        title_check = '临时对话' in page_title

        # 检查页面是否有临时对话提示文本
        temp_dialog_text_check = self.page.evaluate("""() => {
            const pageText = document.body.textContent || document.body.innerText || '';
            return pageText.includes('您已进入临时对话');
        }""")

        return url_check or title_check or temp_dialog_text_check

    def _verify_model_selection(self, expected_model: str) -> bool:
        """验证当前选择的模型"""
        current_model = self.page.evaluate("""() => {
            const modelButtons = document.querySelectorAll('button');
            for (let button of modelButtons) {
                if (button.textContent.includes('Hunyuan') || button.textContent.includes('DeepSeek')) {
                    return button.textContent.trim();
                }
            }
            return null;
        }""")
        return expected_model in current_model if current_model else False

    def _verify_deep_thinking(self) -> bool:
        """验证深度思考功能是否启用"""
        return self.page.evaluate("""() => {
            const deepThinkingButton = Array.from(document.querySelectorAll('button')).find(btn =>
                btn.textContent.includes('深度思考')
            );
            if (deepThinkingButton) {
                const classList = Array.from(deepThinkingButton.classList);
                return classList.some(cls => cls.includes('checked'));
            }
            return false;
        }""")

    def _verify_web_search(self) -> bool:
        """验证联网搜索功能是否启用"""
        # 检查多种可能的联网状态指示
        network_status_check = self.page.evaluate("""() => {
            const pageText = document.body.textContent || document.body.innerText || '';

            // 检查多种可能的联网状态文本
            const networkIndicators = [
                '点击按钮开启或关闭 联网状态',
                '联网状态',
                '已开启联网',
                '联网搜索已启用',
                '网络搜索'
            ];

            for (let indicator of networkIndicators) {
                if (pageText.includes(indicator)) {
                    return true;
                }
            }

            // 检查是否有联网相关的图标或状态变化
            const networkElements = document.querySelectorAll('[class*="network"], [class*="search"], [title*="联网"], [title*="搜索"]');
            if (networkElements.length > 0) {
                for (let element of networkElements) {
                    const style = window.getComputedStyle(element);
                    // 检查是否有激活状态的样式
                    if (style.color === 'rgb(0, 178, 89)' || style.backgroundColor === 'rgb(223, 241, 232)') {
                        return true;
                    }
                }
            }

            return false;
        }""")

        return network_status_check

    def _upload_files(self, files: List[str]) -> bool:
        """
        上传文件到腾讯元宝

        Args:
            files: 要上传的文件路径列表

        Returns:
            bool: 上传是否成功
        """
        # 腾讯元宝不支持多文件同时上传，只上传第一个文件
        if len(files) > 1:
            files = [files[0]]

        # 设置文件到上传input
        file_input = self.page.locator('input[type="file"]').first
        file_input.set_input_files(files)

        # 等待文件上传完成并验证
        self.page.wait_for_timeout(1000)


    def _wait_for_response_complete(self):
        """等待响应完成"""
        self.page.wait_for_selector('.agent-chat__toolbar__item.agent-chat__toolbar__repeat', timeout=60000)

    def _get_reply_by_copy(self):
        """通过复制功能获取回复"""

        copy_btn = self.page.locator('.agent-chat__toolbar__copy__icon').last
        if copy_btn.is_visible():
            with UniversalClipboardInterceptor(self.page) as interceptor:
                copy_btn.click()
                interceptor.wait_for_capture(timeout=5.0)
                return interceptor.text
        assert False, "复制按钮未找到"

    def chat(self, prompt: str, attachments: Optional[List[str]] = None,
             response_format: str = "text", **kwargs) -> str:
        """
        在当前会话中发送消息并获取回复。

        :param prompt: 用户输入的提示。
        :param attachments: 要上传的附件的本地文件路径列表。
        :param response_format: 响应格式，可选 "text"。
        :return: AI的回复文本。
        """
        # 首先切换到当前驱动的页面
        self.switch_to_page()

        # 1. 上传附件 (如果需要)
        if attachments:
            self._upload_files(attachments)

        # 2. 输入提示
        input_element = self.page.locator('.ql-editor').last
        # 点击输入框激活并输入文本
        input_element.click()
        input_element.fill(prompt)

        # 3. 发送消息
        self.page.locator('#yuanbao-send-btn').click()

        # 4. 等待回复完成并获取回复
        self._wait_for_response_complete()
        return self._get_reply_by_copy()

    def is_ready(self) -> bool:
        """检查平台是否准备好接收新消息"""
        pass

    def save_chat(self, chat_name: str):
        """保存当前会话并命名"""
        pass

    def del_chat(self, chat_name: str):
        """删除会话"""
        pass
