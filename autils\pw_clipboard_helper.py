import time
import threading
from playwright.sync_api import Frame, Page
from typing import Optional, Union

class UniversalClipboardInterceptor:
    """
    一个通过纯JS轮询实现的、工业级的"全能"同步剪贴板拦截器。
    它能同时拦截 document.execCommand('copy')、navigator.clipboard.writeText
    以及 navigator.clipboard.write，并保证100%的资源清理。
    """

    def __init__(self, context: Union[Page, Frame]):
        self._context = context
        # 使用更唯一的命名空间，结合ID和线程ID
        self._namespace = f"__playwright_universal_interceptor_{id(self)}_{threading.get_ident()}"
        self.text: Optional[str] = None

    def __enter__(self):
        js_install_script = f"""
        () => {{
            const ns = '{self._namespace}';
            // 初始化数据存储和清理标志
            if (window[ns]) {{
                // 如果存在，先执行一次清理，以防上次意外中断
                if (window[ns].cleanup) window[ns].cleanup();
            }}

            window[ns] = {{
                text: null,
                copyListener: null,
                originalWriteText: null,
                originalWrite: null,
                cleanup: null,
            }};
            const dataStore = window[ns];

            // --- 拦截机制 1: document.execCommand('copy') ---
            const copyListener = (event) => {{
                try {{
                    const selection = window.getSelection().toString();
                    if (selection) {{
                        dataStore.text = selection;
                    }}
                    // 即使没有选中内容，也要阻止默认行为，因为某些实现会动态将内容放入剪贴板
                    event.stopImmediatePropagation();
                    event.preventDefault();
                }} catch (e) {{
                    console.error('Clipboard interceptor (copy event) error:', e);
                }}
            }};
            dataStore.copyListener = copyListener;
            document.addEventListener('copy', copyListener, {{ capture: true }});

            // --- 拦截机制 2 & 3: Navigator Clipboard API ---
            if (navigator.clipboard) {{
                // 拦截 navigator.clipboard.writeText
                if (typeof navigator.clipboard.writeText === 'function') {{
                    dataStore.originalWriteText = navigator.clipboard.writeText.bind(navigator.clipboard);
                    navigator.clipboard.writeText = async (text) => {{
                        dataStore.text = text;
                        // 返回 resolved Promise，模拟成功
                        return Promise.resolve();
                    }};
                }}

                // 【增强】拦截 navigator.clipboard.write
                if (typeof navigator.clipboard.write === 'function') {{
                    dataStore.originalWrite = navigator.clipboard.write.bind(navigator.clipboard);
                    navigator.clipboard.write = async (data) => {{
                        // data 是一个 ClipboardItem 数组
                        if (Array.isArray(data) && data.length > 0) {{
                            const item = data[0]; // 通常只处理第一个
                            if (item instanceof ClipboardItem && item.types.includes('text/plain')) {{
                                try {{
                                    const blob = await item.getType('text/plain');
                                    dataStore.text = await blob.text();
                                }} catch (e) {{
                                    console.error('Clipboard interceptor (write) error:', e);
                                }}
                            }}
                        }}
                        // 返回 resolved Promise，模拟成功
                        return Promise.resolve();
                    }};
                }}
            }}

            // --- 定义清理函数 ---
            dataStore.cleanup = () => {{
                if (dataStore.copyListener) {{
                    document.removeEventListener('copy', dataStore.copyListener, {{ capture: true }});
                }}
                if (dataStore.originalWriteText) {{
                    navigator.clipboard.writeText = dataStore.originalWriteText;
                }}
                if (dataStore.originalWrite) {{
                    navigator.clipboard.write = dataStore.originalWrite;
                }}
                delete window[ns];
            }};
        }}
        """
        self._context.evaluate(js_install_script)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        # 执行定义在页面上下文中的清理函数
        js_cleanup_script = f"""
        () => {{
            const ns = '{self._namespace}';
            if (window[ns] && typeof window[ns].cleanup === 'function') {{
                window[ns].cleanup();
            }}
        }}
        """
        try:
            self._context.evaluate(js_cleanup_script)
        except Exception as e:
            # 在页面导航或关闭等情况下，评估可能会失败，这是可接受的
            print(f"Clipboard interceptor cleanup failed, possibly due to page navigation: {e}")


    def wait_for_capture(self, timeout: float = 5.0):
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                js_poll_script = f"() => window['{self._namespace}'] ? window['{self._namespace}'].text : null"
                captured_text = self._context.evaluate(js_poll_script)
            except Exception:
                # 如果页面导航或上下文丢失，evaluate会失败，此时应认为捕获失败
                captured_text = None

            if captured_text is not None and captured_text != "":
                self.text = captured_text
                return

            time.sleep(0.1)

        raise TimeoutError(f"通过轮询在 {timeout} 秒内未能捕获到任何剪贴板操作的文本。")