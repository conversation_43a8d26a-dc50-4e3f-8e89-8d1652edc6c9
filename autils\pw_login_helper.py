import traceback

from playwright.sync_api import Page, BrowserContext

PLATFORM_CONFIG = {
    "monica": {
        "check_url": "https://monica.im/home",  # 一个需要登录才能正常访问的页面
        "check_selector": '[class^="user-avatar--"]',  # 登录成功后，页面上肯定会出现的元素
    },
    "aistudio": {
        "check_url": "https://aistudio.google.com/prompts/new_chat",
        "check_selector": 'alkali-accountswitcher',  # 例如用户中心的链接
    },
    "gemini": {
        "check_url": "https://gemini.google.com/app",
        "check_selector": '[aria-label^="Google 账号："]',  # 例如用户中心的链接
    },
    "notebooklm": {
        "check_url": "https://notebooklm.google.com/notebook",
        "check_selector": '[aria-label^="Google 账号："]'  # 例如用户中心的链接
    },
    "cozecn": {
        "check_url": "https://www.coze.cn/space",
        "check_selector": 'text="创建"', # 引号是精确匹配
    },
    "yuanbao": {
        "check_url": "https://yuanbao.tencent.com/chat",
        "check_selector": '.ql-editor', # 输入框表示已登录
    },
    "doubao": {
        "check_url": "https://www.doubao.com/chat/",
        "check_selector": '[data-testid="chat_header_avatar_button"]', # 用户头像按钮表示已登录
    }
    # 在此添加更多平台...
}


def handle_login_check(
    page: Page,
    context: BrowserContext,
    platform: str,
    user: str,
    platform_config: dict,
    storage_path: str,
):
    """
    处理用户的登录检查、手动登录提示和状态保存。
    """
    try:
        print(f"\n正在检查平台 [{platform}] 用户 [{user}] 的登录状态...")
        page.goto(platform_config["check_url"], timeout=15000, wait_until="domcontentloaded")
        page.locator(platform_config["check_selector"]).last.wait_for(state="visible", timeout=5000)
        print(f"用户 [{user}] 已登录。")
        return  # 已登录，直接返回
    except Exception:
        print(f"用户 [{user}] 未登录或会话已过期，需要手动登录。")

    # 提示手动登录
    print("=" * 60)
    print(f"!!! 请在浏览器中为用户 [{user}] 手动登录平台 [{platform}]")
    print("登录成功后，此程序将自动继续...")
    print("=" * 60)
    try:
        # 等待用户手动登录成功
        page.locator(platform_config["check_selector"]).last.wait_for(state="visible", timeout=300000) # 5分钟超时
        print("手动登录成功，正在保存新的登录状态...")
        context.storage_state(path=storage_path)
        print(f"新的状态文件已保存至: {storage_path}")
    except Exception as e:
        print(f"警告：手动登录后仍未检测到登录状态。请检查 selector 是否正确。错误: {e}")
        traceback.print_exc()
        # 抛出异常或根据需要决定是否继续
        raise TimeoutError(f"在为用户 {user} 等待手动登录时超时。")
