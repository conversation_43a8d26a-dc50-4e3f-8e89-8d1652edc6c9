import os
import pytest
import tempfile


@pytest.fixture
def yuanbao_agent(chat_helper_factory, cfg):
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email=cfg.yuanbao.user_email,
        conversation_params={
            "model_name": cfg.yuanbao.model_name
        }
    )
    return yuanbao_agent

def test_basic_chat(cfg, ctx, yuanbao_agent):
    """测试基础聊天功能"""

    response = yuanbao_agent.chat(cfg.yuanbao.basic_chat_prompt)



def test_file_upload_txt(cfg, ctx, yuanbao_agent):
    """测试TXT文件上传功能"""
    # 创建临时TXT文件
    with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as temp_file:
        temp_file.write(cfg.yuanbao.test_file_content)
        temp_file_path = temp_file.name

    # 测试文件上传和AI阅读
    response = yuanbao_agent.chat(cfg.yuanbao.file_upload_prompt, attachments=[temp_file_path])

    # 清理临时文件
    os.remove(temp_file_path)


def test_optional_features(cfg, ctx, chat_helper_factory):
    """测试可选功能：临时会话、模型选择、深度思考、联网搜索"""
    # 测试带有可选功能的配置
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email=cfg.yuanbao.user_email,
        conversation_params={
            "model_name": cfg.yuanbao.model_name,
            "use_temp_session": cfg.yuanbao.use_temp_session,
            "enable_deep_thinking": cfg.yuanbao.enable_deep_thinking,
            "enable_web_search": cfg.yuanbao.enable_web_search
        }
    )

    # 测试基础聊天功能
    response = yuanbao_agent.chat(cfg.yuanbao.basic_chat_prompt)
    if response is None:
        assert False, "可选功能测试响应为空"
    if len(response.strip()) == 0:
        assert False, "可选功能测试响应内容为空"
    if cfg.yuanbao.expected_keyword not in response:
        assert False, f"可选功能测试响应中未包含期望关键词: {cfg.yuanbao.expected_keyword}"

    ctx.optional_features_response = response


def test_config_combinations(cfg, ctx, chat_helper_factory):
    """测试不同配置组合"""
    test_configs = cfg.yuanbao.test_configs
    results = {}

    for config_name, config in test_configs.items():
        # 创建带有特定配置的客户端
        yuanbao_agent = chat_helper_factory(
            platform_name="yuanbao",
            user_email=cfg.yuanbao.user_email,
            conversation_params=config
        )

        # 测试聊天功能
        response = yuanbao_agent.chat(cfg.yuanbao.basic_chat_prompt)
        if response is None:
            assert False, f"配置 {config_name} 测试响应为空"
        if len(response.strip()) == 0:
            assert False, f"配置 {config_name} 测试响应内容为空"
        if cfg.yuanbao.expected_keyword not in response:
            assert False, f"配置 {config_name} 测试响应中未包含期望关键词: {cfg.yuanbao.expected_keyword}"

        results[config_name] = response

    ctx.config_test_results = results


def test_feature_verification_simple(cfg, ctx, chat_helper_factory):
    """测试功能验证机制（简化版）"""
    # 测试带有可选功能的配置，但不强制验证
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email=cfg.yuanbao.user_email,
        conversation_params={
            "model_name": "Hunyuan",
            "use_temp_session": True,
            "enable_deep_thinking": True,
            "enable_web_search": True
        }
    )

    # 测试基础聊天功能
    response = yuanbao_agent.chat(cfg.yuanbao.basic_chat_prompt)
    if response is None:
        assert False, "功能验证测试响应为空"
    if len(response.strip()) == 0:
        assert False, "功能验证测试响应内容为空"
    if cfg.yuanbao.expected_keyword not in response:
        assert False, f"功能验证测试响应中未包含期望关键词: {cfg.yuanbao.expected_keyword}"

    # 获取驱动对象进行验证（不强制要求成功）
    driver = yuanbao_agent.driver

    # 尝试验证各功能状态（记录结果但不失败）
    temp_session_status = driver._verify_temp_session()
    deep_thinking_status = driver._verify_deep_thinking()
    web_search_status = driver._verify_web_search()
    model_status = driver._verify_model_selection("Hunyuan")

    ctx.verification_results = {
        "temp_session": temp_session_status,
        "deep_thinking": deep_thinking_status,
        "web_search": web_search_status,
        "model_selection": model_status,
        "response": response
    }


def test_verification_report(cfg, ctx, chat_helper_factory):
    """生成功能验证报告"""
    if not hasattr(ctx, 'verification_results'):
        assert False, "需要先运行 test_feature_verification_simple"

    results = ctx.verification_results

    # 生成报告
    report = []
    report.append("=== 腾讯元宝功能验证报告 ===")
    report.append(f"临时会话验证: {'✓ 通过' if results['temp_session'] else '✗ 失败'}")
    report.append(f"深度思考验证: {'✓ 通过' if results['deep_thinking'] else '✗ 失败'}")
    report.append(f"联网搜索验证: {'✓ 通过' if results['web_search'] else '✗ 失败'}")
    report.append(f"模型选择验证: {'✓ 通过' if results['model_selection'] else '✗ 失败'}")
    report.append(f"基础聊天功能: {'✓ 正常' if results['response'] else '✗ 异常'}")
    report.append("=" * 30)

    # 输出报告
    for line in report:
        print(line)

    # 至少基础聊天功能要正常
    if not results['response']:
        assert False, "基础聊天功能异常"


def test_debug_verification_methods(cfg, ctx, chat_helper_factory):
    """调试验证方法"""
    # 创建一个带有所有功能的客户端
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email=cfg.yuanbao.user_email,
        conversation_params={
            "model_name": "Hunyuan",
            "use_temp_session": True,
            "enable_deep_thinking": True,
            "enable_web_search": True
        }
    )

    driver = yuanbao_agent.driver

    # 手动调用验证方法并输出详细信息
    print("\n=== 验证方法调试 ===")

    # 临时会话验证
    current_url = driver.page.url
    page_title = driver.page.title()
    temp_dialog_text = driver.page.evaluate("""() => {
        const pageText = document.body.textContent || document.body.innerText || '';
        return pageText.includes('您已进入临时对话');
    }""")

    print(f"当前URL: {current_url}")
    print(f"页面标题: {page_title}")
    print(f"URL包含chatMode=temp: {'chatMode=temp' in current_url}")
    print(f"标题包含临时对话: {'临时对话' in page_title}")
    print(f"页面包含临时对话文本: {temp_dialog_text}")

    temp_session_result = driver._verify_temp_session()
    print(f"临时会话验证结果: {temp_session_result}")

    # 联网搜索验证
    network_status_text = driver.page.evaluate("""() => {
        const pageText = document.body.textContent || document.body.innerText || '';
        return pageText.includes('点击按钮开启或关闭 联网状态');
    }""")

    print(f"页面包含联网状态文本: {network_status_text}")

    web_search_result = driver._verify_web_search()
    print(f"联网搜索验证结果: {web_search_result}")

    # 深度思考验证
    deep_thinking_result = driver._verify_deep_thinking()
    print(f"深度思考验证结果: {deep_thinking_result}")

    # 模型选择验证
    model_result = driver._verify_model_selection("Hunyuan")
    print(f"模型选择验证结果: {model_result}")

    print("=== 调试完成 ===\n")