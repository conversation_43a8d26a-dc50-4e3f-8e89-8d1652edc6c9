"""
腾讯元宝可选功能使用示例

本文件展示如何使用腾讯元宝的各种可选功能：
1. 临时会话
2. 模型选择
3. 深度思考
4. 联网搜索
"""

from allm_helper.llm_begin_helper import chat_helper_factory


def example_basic_usage():
    """基础使用示例"""
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email="17775747276",
        conversation_params={
            "model_name": "Hunyuan"
        }
    )
    
    response = yuanbao_agent.chat("1+1等于多少？")
    print(f"基础使用响应: {response}")


def example_temp_session():
    """临时会话示例"""
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email="17775747276",
        conversation_params={
            "model_name": "Hunyuan",
            "use_temp_session": True  # 启用临时会话
        }
    )
    
    response = yuanbao_agent.chat("这是一个临时会话测试")
    print(f"临时会话响应: {response}")


def example_deep_thinking():
    """深度思考示例"""
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email="17775747276",
        conversation_params={
            "model_name": "DeepSeek",
            "enable_deep_thinking": True  # 启用深度思考
        }
    )
    
    response = yuanbao_agent.chat("请深入分析人工智能的发展趋势")
    print(f"深度思考响应: {response}")


def example_web_search():
    """联网搜索示例"""
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email="17775747276",
        conversation_params={
            "model_name": "Hunyuan",
            "enable_web_search": True  # 启用联网搜索
        }
    )
    
    response = yuanbao_agent.chat("今天的天气如何？")
    print(f"联网搜索响应: {response}")


def example_combined_features():
    """组合功能示例"""
    yuanbao_agent = chat_helper_factory(
        platform_name="yuanbao",
        user_email="17775747276",
        conversation_params={
            "model_name": "DeepSeek",
            "use_temp_session": True,      # 临时会话
            "enable_deep_thinking": True,  # 深度思考
            "enable_web_search": True      # 联网搜索
        }
    )
    
    response = yuanbao_agent.chat("请深入分析最新的AI技术发展动态")
    print(f"组合功能响应: {response}")


if __name__ == "__main__":
    print("腾讯元宝功能示例")
    print("=" * 50)
    
    print("\n1. 基础使用:")
    example_basic_usage()
    
    print("\n2. 临时会话:")
    example_temp_session()
    
    print("\n3. 深度思考:")
    example_deep_thinking()
    
    print("\n4. 联网搜索:")
    example_web_search()
    
    print("\n5. 组合功能:")
    example_combined_features()
